/**
 * CameraUtils - Utility functions for camera positioning and bounding box calculations
 */

import * as THREE from 'three';

/**
 * Calculate practical bounds for zoom-to-fit based on actual rendered positions
 */
export function calculateSolarSystemBounds(celestialBodies, includeOrbits = true) {
  // Find the furthest planet from the sun (origin)
  let furthestDistance = 0;
  let furthestBody = null;

  celestialBodies.forEach(body => {
    if (body.name === 'sun') return; // Skip the sun

    const position = body.getPosition();
    const distanceFromSun = position.length();

    if (distanceFromSun > furthestDistance) {
      furthestDistance = distanceFromSun;
      furthestBody = body;
    }
  });

  // Use practical visualization scale - the furthest planet determines our bounds
  // Add some padding for a good overview
  const systemRadius = furthestDistance * 1.3; // 30% padding beyond furthest planet

  // Ensure minimum reasonable size
  const minSystemRadius = 100; // Minimum 100 units radius
  const finalRadius = Math.max(systemRadius, minSystemRadius);

  // Create symmetric bounds around the origin (sun)
  const bounds = {
    min: new THREE.Vector3(-finalRadius, -finalRadius, -finalRadius),
    max: new THREE.Vector3(finalRadius, finalRadius, finalRadius)
  };

  const center = new THREE.Vector3(0, 0, 0); // Solar system centered at origin
  const size = new THREE.Vector3(finalRadius * 2, finalRadius * 2, finalRadius * 2);
  const maxDimension = finalRadius * 2;

  console.log('📊 Solar system bounds calculated:', {
    furthestPlanet: furthestBody?.name || 'none',
    furthestDistance: furthestDistance.toFixed(2),
    systemRadius: finalRadius.toFixed(2),
    maxDimension: maxDimension.toFixed(2),
    bodyCount: celestialBodies.length
  });

  return {
    min: bounds.min,
    max: bounds.max,
    center,
    size,
    maxDimension,
    systemRadius: finalRadius,
    furthestDistance
  };
}

/**
 * Calculate optimal camera position using practical visualization scale
 */
export function calculateOptimalCameraPosition(bounds, camera, padding = 1.5) {
  const { center, systemRadius } = bounds;

  // Use practical camera distances based on the visualization scale
  // For reference: individual planets use distances of 2-25 units
  // For full solar system, we want to be far enough to see everything
  const baseDistance = systemRadius * padding;

  // Ensure reasonable bounds based on the actual scale used in the app
  const minDistance = Math.max(systemRadius * 1.2, 150); // At least 150 units for overview
  const maxDistance = systemRadius * 3; // No more than 3x the system radius
  const finalDistance = Math.max(minDistance, Math.min(baseDistance, maxDistance));

  // Position camera at a good viewing angle
  // Use a moderate elevation for nice perspective
  const elevationAngle = Math.PI / 6; // 30 degrees
  const azimuthAngle = Math.PI / 4; // 45 degrees around Y axis

  // Calculate position using spherical coordinates
  const horizontalRadius = finalDistance * Math.cos(elevationAngle);
  const height = finalDistance * Math.sin(elevationAngle);

  const cameraPosition = new THREE.Vector3(
    center.x + horizontalRadius * Math.cos(azimuthAngle),
    center.y + height,
    center.z + horizontalRadius * Math.sin(azimuthAngle)
  );

  console.log('📹 Calculated camera position:', {
    systemRadius: systemRadius.toFixed(2),
    finalDistance: finalDistance.toFixed(2),
    position: cameraPosition.toArray().map(v => v.toFixed(2)),
    lookAt: center.toArray().map(v => v.toFixed(2)),
    elevationDegrees: (elevationAngle * 180 / Math.PI).toFixed(1),
    azimuthDegrees: (azimuthAngle * 180 / Math.PI).toFixed(1)
  });

  return {
    position: cameraPosition,
    lookAt: center,
    distance: finalDistance,
    bounds
  };
}

/**
 * Smoothly animate camera to a target position and orientation
 */
export class CameraAnimator {
  constructor(camera, navigationControls = null) {
    this.camera = camera;
    this.navigationControls = navigationControls;
    this.isAnimating = false;
    this.animationId = null;
    this.onComplete = null;
    this.onProgress = null;

    // Animation parameters
    this.duration = 2000; // 2 seconds default
    this.easing = this.easeInOutCubic;
  }

  /**
   * Animate camera to target position and look-at point
   */
  animateTo(targetPosition, targetLookAt, duration = 2000, onComplete = null, onProgress = null) {
    // Cancel any existing animation
    this.stop();

    this.isAnimating = true;
    this.duration = duration;
    this.onComplete = onComplete;
    this.onProgress = onProgress;

    // Store initial state
    const startPosition = this.camera.position.clone();
    const startQuaternion = this.camera.quaternion.clone();

    // Calculate target quaternion by looking at the target
    const tempCamera = new THREE.PerspectiveCamera();
    tempCamera.position.copy(targetPosition);
    tempCamera.lookAt(targetLookAt);
    const targetQuaternion = tempCamera.quaternion.clone();

    // Pause navigation controls during animation
    if (this.navigationControls) {
      this.navigationControls.pause();
    }

    const startTime = performance.now();

    const animate = (currentTime) => {
      if (!this.isAnimating) return;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / this.duration, 1);
      const easedProgress = this.easing(progress);

      // Interpolate position
      this.camera.position.lerpVectors(startPosition, targetPosition, easedProgress);

      // Interpolate rotation
      this.camera.quaternion.slerpQuaternions(startQuaternion, targetQuaternion, easedProgress);

      // Call progress callback
      if (this.onProgress) {
        this.onProgress(progress);
      }

      if (progress >= 1) {
        // Animation complete
        this.stop();
        
        // Sync navigation controls with final camera state
        if (this.navigationControls && this.navigationControls.syncEulerWithCamera) {
          this.navigationControls.syncEulerWithCamera();
        }

        if (this.onComplete) {
          this.onComplete();
        }
      } else {
        this.animationId = requestAnimationFrame(animate);
      }
    };

    this.animationId = requestAnimationFrame(animate);
  }

  /**
   * Stop the current animation
   */
  stop() {
    this.isAnimating = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    // Resume navigation controls
    if (this.navigationControls) {
      this.navigationControls.resume();
    }
  }

  /**
   * Ease-in-out cubic easing function
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * Check if animation is currently running
   */
  isRunning() {
    return this.isAnimating;
  }
}

/**
 * Get the furthest celestial body from the center for bounds calculation
 */
export function getFurthestBody(celestialBodies) {
  let furthestDistance = 0;
  let furthestBody = null;

  celestialBodies.forEach(body => {
    if (body.name === 'sun') return; // Skip the sun

    const position = body.getPosition();
    const distance = position.length(); // Distance from origin (sun)

    if (distance > furthestDistance) {
      furthestDistance = distance;
      furthestBody = body;
    }
  });

  return { body: furthestBody, distance: furthestDistance };
}

/**
 * Calculate camera position for different viewing modes
 */
export function calculateViewModePosition(bounds, mode = 'overview') {
  const { center, maxDimension } = bounds;

  switch (mode) {
    case 'overview':
      // Standard overview position
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.2);

    case 'wide':
      // Wider view with more padding
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.8);

    case 'tight':
      // Closer view with less padding
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.0);

    case 'top':
      // Top-down view
      return {
        position: new THREE.Vector3(center.x, center.y + maxDimension, center.z),
        lookAt: center
      };

    case 'side':
      // Side view
      return {
        position: new THREE.Vector3(center.x + maxDimension, center.y, center.z),
        lookAt: center
      };

    default:
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.2);
  }
}
