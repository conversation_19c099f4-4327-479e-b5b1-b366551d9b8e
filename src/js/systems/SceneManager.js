/**
 * SceneManager - Manages the Three.js scene, camera, renderer, and lighting
 */

import * as THREE from 'three';
import { CelestialBody } from '../models/CelestialBody.js';
import { SOLAR_SYSTEM_DATA, MOONS_DATA, getScaledData } from '../data/SolarSystemData.js';
import { PlanetTravelSystem } from './PlanetTravelSystem.js';
import { calculateSolarSystemBounds, calculateOptimalCameraPosition, CameraAnimator } from '../utils/CameraUtils.js';

export class SceneManager {
  constructor(container) {
    this.container = container;

    // Core Three.js components
    this.scene = null;
    this.camera = null;
    this.renderer = null;

    // Lighting
    this.ambientLight = null;
    this.sunLight = null;

    // Scene objects
    this.celestialBodies = new Map();
    this.starField = null;

    // Systems
    this.planetTravelSystem = null;
    this.cameraAnimator = null;

    // Settings
    this.currentMode = 'exploration'; // 'realistic', 'exploration', or 'artistic'
    this.timeScale = 1.0;

    // Telescope system for realistic mode
    this.telescopeSystem = null;

    // Distant object indicators
    this.distantObjectIndicators = null;

    // Performance
    this.renderTarget = null;
  }

  /**
   * Initialize the scene manager
   */
  async init() {
    this.createScene();
    this.createCamera();
    this.createRenderer();
    this.createLighting();
    await this.createStarField();
    this.initializeIndicatorSystems();

    console.log('✅ SceneManager initialized');
  }

  /**
   * Initialize indicator systems
   */
  async initializeIndicatorSystems() {
    // Initialize distant object indicators
    const { DistantObjectIndicators } = await import('./DistantObjectIndicators.js');
    this.distantObjectIndicators = new DistantObjectIndicators(this.scene, this.camera, this.renderer);

    console.log('✅ Indicator systems initialized');
  }

  /**
   * Initialize the planet travel system (called after navigation controls are ready)
   */
  initializeTravelSystem(navigationControls) {
    this.planetTravelSystem = new PlanetTravelSystem(
      this.camera,
      this,
      navigationControls
    );

    // Initialize camera animator for zoom-to-fit functionality
    this.cameraAnimator = new CameraAnimator(this.camera, navigationControls);

    console.log('✅ Planet travel system initialized');
    console.log('✅ Camera animator initialized');
  }

  /**
   * Create the Three.js scene
   */
  createScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000000);

    // Add fog for depth perception (disabled by default)
    // this.scene.fog = new THREE.Fog(0x000000, 1000, 10000);
  }

  /**
   * Create the camera
   */
  createCamera() {
    const aspect = this.container.clientWidth / this.container.clientHeight;

    this.camera = new THREE.PerspectiveCamera(
      60, // Field of view
      aspect, // Aspect ratio
      0.1, // Near clipping plane
      100000 // Far clipping plane
    );

    // Set initial camera position
    this.camera.position.set(0, 0, 50);
    this.camera.lookAt(0, 0, 0);
  }

  /**
   * Create the WebGL renderer
   */
  createRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: false,
      powerPreference: 'high-performance',
    });

    // Configure renderer
    this.renderer.setSize(
      this.container.clientWidth,
      this.container.clientHeight
    );
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    // Enable shadows
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // Set color space
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;

    // Enable tone mapping for better lighting
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.0;

    // Append to container
    this.container.appendChild(this.renderer.domElement);
  }

  /**
   * Create lighting system
   */
  createLighting() {
    // Ambient light for general illumination (increased for debugging)
    this.ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(this.ambientLight);

    // Sun light (point light at origin)
    this.sunLight = new THREE.PointLight(0xffffff, 2, 0);
    this.sunLight.position.set(0, 0, 0);
    this.sunLight.castShadow = true;

    // Configure shadow properties
    this.sunLight.shadow.mapSize.width = 2048;
    this.sunLight.shadow.mapSize.height = 2048;
    this.sunLight.shadow.camera.near = 0.1;
    this.sunLight.shadow.camera.far = 1000;

    this.scene.add(this.sunLight);
  }

  /**
   * Create a starfield background
   */
  async createStarField() {
    const starCount = 10000;
    const starGeometry = new THREE.BufferGeometry();
    const starPositions = new Float32Array(starCount * 3);
    const starColors = new Float32Array(starCount * 3);

    // Generate random star positions and colors
    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3;

      // Random position on a sphere
      const radius = 50000;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.acos(Math.random() * 2 - 1);

      starPositions[i3] = radius * Math.sin(phi) * Math.cos(theta);
      starPositions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      starPositions[i3 + 2] = radius * Math.cos(phi);

      // Random star color (bluish to reddish)
      const temperature = Math.random();
      starColors[i3] = 0.5 + temperature * 0.5; // Red
      starColors[i3 + 1] = 0.5 + temperature * 0.3; // Green
      starColors[i3 + 2] = 0.8 + temperature * 0.2; // Blue
    }

    starGeometry.setAttribute(
      'position',
      new THREE.BufferAttribute(starPositions, 3)
    );
    starGeometry.setAttribute(
      'color',
      new THREE.BufferAttribute(starColors, 3)
    );

    const starMaterial = new THREE.PointsMaterial({
      size: 2,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
    });

    this.starField = new THREE.Points(starGeometry, starMaterial);
    this.scene.add(this.starField);
  }

  /**
   * Load solar system data and create celestial bodies
   */
  async loadSolarSystem() {
    console.log('🌌 Loading solar system...');

    // Clear existing bodies
    this.celestialBodies.clear();

    // Create all planets and the sun
    const bodyNames = Object.keys(SOLAR_SYSTEM_DATA);
    const loadPromises = [];

    for (const bodyName of bodyNames) {
      const bodyData = getScaledData(bodyName, this.currentMode);
      const celestialBody = new CelestialBody(bodyName, bodyData);

      // Store the celestial body
      this.celestialBodies.set(bodyName, celestialBody);

      // Add to scene
      this.scene.add(celestialBody.getObject3D());

      // Add creation promise
      loadPromises.push(celestialBody.create());
    }

    // Create major moons
    const moonNames = Object.keys(MOONS_DATA);
    for (const moonName of moonNames) {
      const moonData = getScaledData(moonName, this.currentMode);
      const moon = new CelestialBody(moonName, moonData);

      // Store the moon
      this.celestialBodies.set(moonName, moon);

      // Add to parent planet's group (for now, add to scene)
      this.scene.add(moon.getObject3D());

      // Add creation promise
      loadPromises.push(moon.create());
    }

    // Wait for all bodies to be created
    await Promise.all(loadPromises);

    // Add orbit lines to the scene
    this.addOrbitLinesToScene();

    console.log(`✅ Loaded ${this.celestialBodies.size} celestial bodies`);
  }

  /**
   * Update the scene
   */
  update(deltaTime) {
    // Update celestial body animations
    this.celestialBodies.forEach((body) => {
      if (body.update) {
        body.update(deltaTime, this.timeScale);
      }
    });

    // Update planet travel system
    if (this.planetTravelSystem) {
      this.planetTravelSystem.update(deltaTime);
    }

    // Update telescope system
    if (this.telescopeSystem) {
      this.telescopeSystem.updateTracking();
    }

    // Update distant object indicators
    if (this.distantObjectIndicators) {
      this.distantObjectIndicators.updateIndicators(this.celestialBodies, this.currentMode);
      this.distantObjectIndicators.updatePositions();
    }

    // Update orbit lines performance optimization
    this.updateOrbitLinesPerformance();

    // Subtle starfield rotation
    if (this.starField) {
      this.starField.rotation.y += deltaTime * 0.001;
    }
  }

  /**
   * Render the scene
   */
  render() {
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * Handle window resize
   */
  onWindowResize() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    // Update camera
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();

    // Update renderer
    this.renderer.setSize(width, height);
  }

  /**
   * Switch between realistic, exploration, and artistic modes
   */
  async setVisualizationMode(mode) {
    if (mode === this.currentMode) return;

    const previousMode = this.currentMode;
    this.currentMode = mode;

    // Update lighting based on mode
    switch (mode) {
      case 'realistic':
        // True astronomical mode - minimal lighting
        this.ambientLight.intensity = 0.1;
        this.sunLight.intensity = 3.0;
        this.renderer.toneMappingExposure = 0.8;

        // Initialize telescope system if not already created
        if (!this.telescopeSystem) {
          const { TelescopeSystem } = await import('./TelescopeSystem.js');
          this.telescopeSystem = new TelescopeSystem(this.camera, this.scene);
        }
        break;

      case 'exploration':
        // Balanced mode for navigation
        this.ambientLight.intensity = 0.3;
        this.sunLight.intensity = 2.0;
        this.renderer.toneMappingExposure = 1.0;

        // Disable telescope if active
        if (this.telescopeSystem && this.telescopeSystem.isTelescopeMode) {
          this.telescopeSystem.toggleTelescopeMode();
        }
        break;

      case 'artistic':
        // Enhanced visuals for beauty
        this.ambientLight.intensity = 0.5;
        this.sunLight.intensity = 1.5;
        this.renderer.toneMappingExposure = 1.2;

        // Disable telescope if active
        if (this.telescopeSystem && this.telescopeSystem.isTelescopeMode) {
          this.telescopeSystem.toggleTelescopeMode();
        }
        break;

      default:
        console.warn(`Unknown visualization mode: ${mode}`);
        this.currentMode = previousMode;
        return;
    }

    // Reload solar system with new scaling
    console.log(`🎨 Switching from ${previousMode} to ${mode} mode - reloading solar system...`);
    await this.reloadSolarSystem();

    // Update celestial body modes
    this.celestialBodies.forEach((body) => {
      if (body.setMode) {
        body.setMode(mode);
      }
    });

    // Optimize orbit lines based on mode
    this.optimizeOrbitLinesForMode(mode);

    console.log(`🎨 Switched to ${mode} mode`);

    // Show mode-specific notifications
    this.showModeNotification(mode);
  }

  /**
   * Show notification about current mode capabilities
   */
  showModeNotification(mode) {
    const notifications = {
      realistic: '🔭 Realistic Mode: True astronomical distances. Use telescope (T) to view distant planets.',
      exploration: '🚀 Exploration Mode: Balanced distances for navigation and learning.',
      artistic: '🎨 Artistic Mode: Enhanced visuals and compressed distances for beauty.'
    };

    console.log(notifications[mode] || '');
  }

  /**
   * Reload the solar system (useful when switching modes)
   */
  async reloadSolarSystem() {
    // Dispose of existing bodies and their orbit lines
    this.celestialBodies.forEach((body) => {
      // Remove orbit line from scene
      const orbitLine = body.getOrbitLine();
      if (orbitLine && orbitLine.getObject3D()) {
        this.scene.remove(orbitLine.getObject3D());
      }

      if (body.dispose) {
        body.dispose();
      }
      // Remove from scene
      this.scene.remove(body.getObject3D());
    });

    // Reload with current mode
    await this.loadSolarSystem();
  }

  /**
   * Get celestial body by name
   */
  getCelestialBody(name) {
    return this.celestialBodies.get(name);
  }

  /**
   * Get all celestial bodies
   */
  getAllCelestialBodies() {
    return Array.from(this.celestialBodies.values());
  }

  /**
   * Add orbit lines to the scene
   */
  addOrbitLinesToScene() {
    this.celestialBodies.forEach((body) => {
      const orbitLine = body.getOrbitLine();
      if (orbitLine && orbitLine.getObject3D()) {
        this.scene.add(orbitLine.getObject3D());
        console.log(`🛸 Added orbit line for ${body.name} to scene`);
      }
    });
  }

  /**
   * Toggle orbit line visibility for all celestial bodies
   */
  toggleOrbitLines() {
    let anyVisible = false;

    // Check if any orbit lines are currently visible
    this.celestialBodies.forEach((body) => {
      if (body.isOrbitLineVisible()) {
        anyVisible = true;
      }
    });

    // Toggle all orbit lines to the opposite state
    const newVisibility = !anyVisible;
    this.setOrbitLinesVisible(newVisibility);

    console.log(`🛸 ${newVisibility ? 'Enabled' : 'Disabled'} orbit lines`);
    return newVisibility;
  }

  /**
   * Set orbit line visibility for all celestial bodies
   */
  setOrbitLinesVisible(visible) {
    this.celestialBodies.forEach((body) => {
      body.setOrbitLineVisible(visible);
    });
  }

  /**
   * Get orbit line visibility status
   */
  areOrbitLinesVisible() {
    let anyVisible = false;
    this.celestialBodies.forEach((body) => {
      if (body.isOrbitLineVisible()) {
        anyVisible = true;
      }
    });
    return anyVisible;
  }

  /**
   * Update orbit lines performance based on camera position
   */
  updateOrbitLinesPerformance() {
    if (!this.camera) return;

    const cameraPosition = this.camera.position;

    // Update each orbit line's level of detail based on camera distance
    this.celestialBodies.forEach((body) => {
      const orbitLine = body.getOrbitLine();
      if (orbitLine && orbitLine.isOrbitVisible()) {
        orbitLine.updateLevelOfDetail(cameraPosition);
      }
    });
  }

  /**
   * Optimize orbit lines for performance mode
   */
  optimizeOrbitLinesForPerformance() {
    this.celestialBodies.forEach((body) => {
      const orbitLine = body.getOrbitLine();
      if (orbitLine) {
        orbitLine.optimizeForPerformance();
      }
    });
    console.log('🚀 Optimized orbit lines for performance');
  }

  /**
   * Enable high-quality orbit line rendering
   */
  enableHighQualityOrbitLines() {
    this.celestialBodies.forEach((body) => {
      const orbitLine = body.getOrbitLine();
      if (orbitLine) {
        orbitLine.enableHighQuality();
      }
    });
    console.log('✨ Enabled high-quality orbit line rendering');
  }

  /**
   * Optimize orbit lines based on visualization mode
   */
  optimizeOrbitLinesForMode(mode) {
    switch (mode) {
      case 'realistic':
        // Optimize for performance in realistic mode due to large distances
        this.optimizeOrbitLinesForPerformance();
        break;
      case 'exploration':
        // Balanced quality for exploration mode
        this.celestialBodies.forEach((body) => {
          const orbitLine = body.getOrbitLine();
          if (orbitLine) {
            orbitLine.setLevelOfDetail('medium');
          }
        });
        break;
      case 'artistic':
        // High quality for artistic mode
        this.enableHighQualityOrbitLines();
        break;
    }
  }

  /**
   * Get planet travel system
   */
  getPlanetTravelSystem() {
    return this.planetTravelSystem;
  }

  /**
   * Get telescope system
   */
  getTelescopeSystem() {
    return this.telescopeSystem;
  }

  /**
   * Toggle telescope mode (only available in realistic mode)
   */
  toggleTelescopeMode() {
    if (this.currentMode !== 'realistic') {
      console.warn('🔭 Telescope mode only available in realistic mode');
      return false;
    }

    if (!this.telescopeSystem) {
      console.warn('🔭 Telescope system not initialized');
      return false;
    }

    return this.telescopeSystem.toggleTelescopeMode();
  }

  /**
   * Travel to a celestial body
   */
  async travelTo(bodyName, speed = 1.0) {
    if (!this.planetTravelSystem) {
      console.warn('Planet travel system not initialized');
      return false;
    }

    return await this.planetTravelSystem.travelTo(bodyName, speed);
  }

  /**
   * Zoom camera to fit the entire solar system in view
   */
  zoomToFit(duration = 2000, padding = 1.2) {
    if (!this.cameraAnimator) {
      console.warn('Camera animator not initialized');
      return Promise.reject(new Error('Camera animator not available'));
    }

    // Check if already animating
    if (this.cameraAnimator.isRunning()) {
      console.log('🎬 Camera animation already in progress, stopping current animation');
      this.cameraAnimator.stop();
    }

    // Get all celestial bodies
    const celestialBodies = this.getAllCelestialBodies();

    if (celestialBodies.length === 0) {
      console.warn('No celestial bodies found for zoom-to-fit');
      return Promise.reject(new Error('No celestial bodies available'));
    }

    // Automatically enable orbital planes if they are currently disabled
    if (!this.areOrbitLinesVisible()) {
      console.log('🛸 Enabling orbit lines for zoom-to-fit view');
      this.setOrbitLinesVisible(true);
    }

    // Calculate solar system bounds with mode-specific adjustments
    const bounds = calculateSolarSystemBounds(celestialBodies, true, this.currentMode);

    console.log('🔍 Solar system bounds calculated:', {
      mode: this.currentMode,
      center: bounds.center.toArray().map(v => v.toFixed(2)),
      systemRadius: bounds.systemRadius.toFixed(2),
      furthestDistance: bounds.furthestDistance.toFixed(2)
    });

    // Calculate optimal camera position with mode-specific padding
    const modePadding = this.getZoomToFitPadding(padding);
    const cameraTarget = calculateOptimalCameraPosition(bounds, this.camera, modePadding);

    // Validate camera position and use fallback if needed
    if (!isFinite(cameraTarget.position.x) || !isFinite(cameraTarget.position.y) || !isFinite(cameraTarget.position.z)) {
      console.warn('⚠️ Invalid camera position calculated, using fallback');
      const fallbackDistance = this.getFallbackCameraDistance(bounds);
      cameraTarget.position.set(fallbackDistance, fallbackDistance * 0.5, fallbackDistance);
      cameraTarget.lookAt.set(0, 0, 0);
      cameraTarget.distance = fallbackDistance;
    }

    // Additional validation for extreme values
    const maxAllowedDistance = this.getMaxAllowedCameraDistance();
    const currentDistance = cameraTarget.position.length();

    if (currentDistance > maxAllowedDistance) {
      console.warn(`⚠️ Camera distance too large (${currentDistance.toFixed(2)}), capping at ${maxAllowedDistance}`);
      const direction = cameraTarget.position.clone().normalize();
      cameraTarget.position.copy(direction.multiplyScalar(maxAllowedDistance));
      cameraTarget.distance = maxAllowedDistance;
    }

    console.log('📹 Zoom to fit - Camera target:', {
      mode: this.currentMode,
      position: cameraTarget.position.toArray().map(v => v.toFixed(2)),
      lookAt: cameraTarget.lookAt.toArray().map(v => v.toFixed(2)),
      distance: cameraTarget.distance?.toFixed(2) || 'N/A'
    });

    // Return a promise that resolves when animation completes
    return new Promise((resolve, reject) => {
      try {
        this.cameraAnimator.animateTo(
          cameraTarget.position,
          cameraTarget.lookAt,
          duration,
          () => {
            console.log('✅ Zoom to fit animation completed');
            resolve();
          },
          (progress) => {
            // Optional: could emit progress events here
            if (progress === 0.5) {
              console.log('🎬 Zoom to fit animation 50% complete');
            }
          }
        );
      } catch (error) {
        console.error('❌ Zoom to fit animation failed:', error);
        reject(error);
      }
    });
  }

  /**
   * Get appropriate padding for zoom-to-fit based on visualization mode
   */
  getZoomToFitPadding(basePadding = 1.2) {
    switch (this.currentMode) {
      case 'realistic':
        // In realistic mode, use less padding since distances are huge
        return Math.max(basePadding * 0.8, 1.1);
      case 'exploration':
        // Standard padding for exploration mode
        return basePadding;
      case 'artistic':
        // More padding for artistic mode to show enhanced visuals
        return basePadding * 1.3;
      default:
        return basePadding;
    }
  }

  /**
   * Get fallback camera distance based on visualization mode and bounds
   */
  getFallbackCameraDistance(bounds) {
    const baseDistance = Math.max(bounds.systemRadius * 2, 100);

    switch (this.currentMode) {
      case 'realistic':
        // In realistic mode, use a more conservative fallback
        return Math.min(baseDistance, 1000); // Cap at 1000 units
      case 'exploration':
        // Standard fallback for exploration mode
        return Math.min(baseDistance, 500);
      case 'artistic':
        // Closer fallback for artistic mode
        return Math.min(baseDistance, 300);
      default:
        return Math.min(baseDistance, 500);
    }
  }

  /**
   * Get maximum allowed camera distance to prevent excessive zoom-out
   */
  getMaxAllowedCameraDistance() {
    switch (this.currentMode) {
      case 'realistic':
        // In realistic mode, allow larger distances but still cap them
        return 5000; // Maximum 5000 units
      case 'exploration':
        // Standard maximum for exploration mode
        return 2000; // Maximum 2000 units
      case 'artistic':
        // Smaller maximum for artistic mode to keep things intimate
        return 1000; // Maximum 1000 units
      default:
        return 2000;
    }
  }

  /**
   * Get the camera animator instance
   */
  getCameraAnimator() {
    return this.cameraAnimator;
  }

  /**
   * Check if camera is currently animating
   */
  isCameraAnimating() {
    return this.cameraAnimator ? this.cameraAnimator.isRunning() : false;
  }

  /**
   * Dispose of resources
   */
  dispose() {
    // Dispose of celestial bodies
    this.celestialBodies.forEach((body) => {
      if (body.dispose) {
        body.dispose();
      }
    });

    // Dispose of starfield
    if (this.starField) {
      this.starField.geometry.dispose();
      this.starField.material.dispose();
    }

    // Dispose of renderer
    if (this.renderer) {
      this.renderer.dispose();
      if (this.renderer.domElement.parentNode) {
        this.renderer.domElement.parentNode.removeChild(
          this.renderer.domElement
        );
      }
    }

    console.log('🧹 SceneManager disposed');
  }
}
