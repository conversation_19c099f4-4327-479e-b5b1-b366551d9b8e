/**
 * Test script for debugging mode switching issues
 * Run this in the browser console to test and debug mode switching
 */

// Test mode switching functionality
async function testModeSwitch() {
  console.log('🧪 Testing mode switch functionality...');
  
  const app = window.solarSystemApp;
  if (!app) {
    console.error('❌ Solar system app not found. Make sure the app is loaded.');
    return;
  }
  
  const sceneManager = app.sceneManager;
  const uiManager = app.uiManager;
  
  if (!sceneManager || !uiManager) {
    console.error('❌ Scene manager or UI manager not found.');
    return;
  }
  
  console.log(`📊 Current mode: ${sceneManager.currentMode}`);
  console.log(`📊 UI current mode: ${uiManager.currentMode}`);
  
  // Test each mode switch
  const modes = ['exploration', 'realistic', 'artistic'];
  
  for (const mode of modes) {
    if (mode === sceneManager.currentMode) continue;
    
    console.log(`\n🎨 Testing switch to ${mode} mode...`);
    
    try {
      const startTime = performance.now();
      await sceneManager.setVisualizationMode(mode);
      const endTime = performance.now();
      
      console.log(`✅ Successfully switched to ${mode} mode in ${(endTime - startTime).toFixed(2)}ms`);
      console.log(`📊 Scene manager mode: ${sceneManager.currentMode}`);
      
      // Wait a moment between switches
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(`❌ Failed to switch to ${mode} mode:`, error);
    }
  }
}

// Test UI button state
function testButtonState() {
  console.log('🧪 Testing button state...');
  
  const app = window.solarSystemApp;
  if (!app || !app.uiManager) {
    console.error('❌ UI manager not found.');
    return;
  }
  
  const uiManager = app.uiManager;
  const button = uiManager.modeToggle;
  
  if (!button) {
    console.error('❌ Mode toggle button not found.');
    return;
  }
  
  console.log(`📊 Button disabled: ${button.disabled}`);
  console.log(`📊 Button text: "${button.textContent}"`);
  console.log(`📊 Button class: "${button.className}"`);
  
  // Check for stuck state
  if (button.disabled && button.textContent === 'Switching...') {
    console.warn('⚠️ Button appears to be stuck in switching state!');
    console.log('🔧 Attempting to reset button state...');
    uiManager.resetModeToggleState();
  }
}

// Force reset button if stuck
function forceResetButton() {
  console.log('🔧 Force resetting mode toggle button...');
  
  const app = window.solarSystemApp;
  if (!app || !app.uiManager) {
    console.error('❌ UI manager not found.');
    return;
  }
  
  const uiManager = app.uiManager;
  uiManager.resetModeToggleState();
  console.log('✅ Button reset completed');
}

// Test mode switching with UI button
async function testUIButtonModeSwitch() {
  console.log('🧪 Testing UI button mode switch...');
  
  const app = window.solarSystemApp;
  if (!app || !app.uiManager) {
    console.error('❌ UI manager not found.');
    return;
  }
  
  const uiManager = app.uiManager;
  const button = uiManager.modeToggle;
  
  if (!button) {
    console.error('❌ Mode toggle button not found.');
    return;
  }
  
  if (button.disabled) {
    console.warn('⚠️ Button is currently disabled, resetting first...');
    uiManager.resetModeToggleState();
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`📊 Starting mode: ${uiManager.currentMode}`);
  console.log('🖱️ Simulating button click...');
  
  // Monitor button state
  const checkInterval = setInterval(() => {
    console.log(`📊 Button state: disabled=${button.disabled}, text="${button.textContent}"`);
  }, 1000);
  
  try {
    // Simulate button click
    button.click();
    
    // Wait for completion (max 40 seconds)
    let attempts = 0;
    while (button.disabled && attempts < 40) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    }
    
    clearInterval(checkInterval);
    
    if (button.disabled) {
      console.error('❌ Button still disabled after 40 seconds - likely stuck!');
      uiManager.resetModeToggleState();
    } else {
      console.log(`✅ Mode switch completed. New mode: ${uiManager.currentMode}`);
    }
    
  } catch (error) {
    clearInterval(checkInterval);
    console.error('❌ Error during UI button test:', error);
  }
}

// Monitor mode switching in real-time
function monitorModeSwitch() {
  console.log('👁️ Starting mode switch monitor...');
  
  const app = window.solarSystemApp;
  if (!app || !app.uiManager) {
    console.error('❌ UI manager not found.');
    return;
  }
  
  const uiManager = app.uiManager;
  const button = uiManager.modeToggle;
  
  if (!button) {
    console.error('❌ Mode toggle button not found.');
    return;
  }
  
  let lastState = {
    disabled: button.disabled,
    text: button.textContent,
    mode: uiManager.currentMode
  };
  
  console.log('📊 Initial state:', lastState);
  
  const monitor = setInterval(() => {
    const currentState = {
      disabled: button.disabled,
      text: button.textContent,
      mode: uiManager.currentMode
    };
    
    if (JSON.stringify(currentState) !== JSON.stringify(lastState)) {
      console.log('🔄 State changed:', currentState);
      lastState = currentState;
    }
  }, 500);
  
  // Stop monitoring after 2 minutes
  setTimeout(() => {
    clearInterval(monitor);
    console.log('👁️ Mode switch monitoring stopped');
  }, 120000);
  
  return monitor;
}

// Export functions for manual testing
window.testModeSwitch = testModeSwitch;
window.testButtonState = testButtonState;
window.forceResetButton = forceResetButton;
window.testUIButtonModeSwitch = testUIButtonModeSwitch;
window.monitorModeSwitch = monitorModeSwitch;

console.log('🧪 Mode switch test functions loaded.');
console.log('Available functions:');
console.log('- testModeSwitch() - Test direct mode switching');
console.log('- testButtonState() - Check current button state');
console.log('- forceResetButton() - Force reset stuck button');
console.log('- testUIButtonModeSwitch() - Test UI button clicking');
console.log('- monitorModeSwitch() - Monitor state changes in real-time');
console.log('');
console.log('💡 If button is stuck, try: forceResetButton()');
