/**
 * UIManager - Manages user interface interactions and updates
 */

export class UIManager {
  constructor(sceneManager, navigationControls) {
    this.sceneManager = sceneManager;
    this.navigationControls = navigationControls;

    // UI elements
    this.modeToggle = null;
    this.speedSlider = null;
    this.speedValue = null;
    this.orbitLinesToggle = null;
    this.planetButtons = null;
    this.helpToggle = null;
    this.helpPanel = null;
    this.infoPanel = null;
    this.currentTarget = null;
    this.targetInfo = null;

    // State
    this.currentMode = 'exploration'; // Start with exploration mode
    this.isHelpVisible = false;
    this.selectedPlanet = null;
    this.debugMode = false;

    // Mode cycle order
    this.modeOrder = ['exploration', 'realistic', 'artistic'];

    // Bind methods
    this.onModeToggle = this.onModeToggle.bind(this);
    this.onSpeedChange = this.onSpeedChange.bind(this);
    this.onOrbitLinesToggle = this.onOrbitLinesToggle.bind(this);
    this.onPlanetSelect = this.onPlanetSelect.bind(this);
    this.onHelpToggle = this.onHelpToggle.bind(this);
  }

  /**
   * Initialize the UI manager
   */
  init() {
    this.setupElements();
    this.setupEventListeners();
    this.updateUI();

    console.log('✅ UIManager initialized');
  }

  /**
   * Set up UI element references
   */
  setupElements() {
    // Mode toggle
    this.modeToggle = document.getElementById('mode-toggle');

    // Speed controls
    this.speedSlider = document.getElementById('speed-slider');
    this.speedValue = document.getElementById('speed-value');

    // Orbit lines toggle
    this.orbitLinesToggle = document.getElementById('orbit-lines-toggle');

    // Zoom to fit button
    this.zoomToFitButton = document.getElementById('zoom-to-fit-button');

    // Planet buttons
    this.planetButtons = document.querySelectorAll('.planet-button');

    // Help panel
    this.helpToggle = document.getElementById('help-toggle');
    this.helpPanel = document.getElementById('help-panel');

    // Info panel
    this.infoPanel = document.getElementById('info-panel');
    this.currentTarget = document.getElementById('current-target');
    this.targetInfo = document.getElementById('target-info');

    // Check if all elements are found
    const elements = [
      this.modeToggle,
      this.speedSlider,
      this.speedValue,
      this.orbitLinesToggle,
      this.zoomToFitButton,
      this.helpToggle,
      this.helpPanel,
      this.infoPanel,
      this.currentTarget,
      this.targetInfo,
    ];

    const missingElements = elements.filter(el => !el);
    if (missingElements.length > 0) {
      console.warn('Some UI elements not found:', missingElements);
    }
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Mode toggle
    if (this.modeToggle) {
      this.modeToggle.addEventListener('click', this.onModeToggle);
    }

    // Speed slider
    if (this.speedSlider) {
      this.speedSlider.addEventListener('input', this.onSpeedChange);
    }

    // Orbit lines toggle
    if (this.orbitLinesToggle) {
      this.orbitLinesToggle.addEventListener('change', this.onOrbitLinesToggle);
    }

    // Zoom to fit button
    if (this.zoomToFitButton) {
      this.zoomToFitButton.addEventListener('click', this.onZoomToFit.bind(this));
    }

    // Planet buttons
    this.planetButtons.forEach(button => {
      button.addEventListener('click', this.onPlanetSelect);
    });

    // Help toggle
    if (this.helpToggle) {
      this.helpToggle.addEventListener('click', this.onHelpToggle);
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', this.onKeyDown.bind(this));
  }

  /**
   * Handle mode toggle - cycles through exploration -> realistic -> artistic
   */
  async onModeToggle() {
    // Check if button is already disabled (mode switch in progress)
    if (this.modeToggle && this.modeToggle.disabled) {
      console.log('🔄 Mode switch already in progress, ignoring click');
      return;
    }

    // Check if traveling - don't allow mode switch during travel
    const travelSystem = this.sceneManager.getPlanetTravelSystem();
    if (travelSystem && travelSystem.isTransitioning) {
      console.log(`⚠️ Cannot switch modes while traveling`);
      this.showNotification('Cannot switch modes while traveling', 'warning');
      return;
    }

    // Cycle to next mode
    const currentIndex = this.modeOrder.indexOf(this.currentMode);
    const nextIndex = (currentIndex + 1) % this.modeOrder.length;
    const targetMode = this.modeOrder[nextIndex];

    console.log(`🎨 Starting mode switch: ${this.currentMode} → ${targetMode}`);

    // Show loading state
    if (this.modeToggle) {
      this.modeToggle.disabled = true;
      this.modeToggle.textContent = 'Switching...';
    }

    // Safety timeout to re-enable button if something goes wrong
    const safetyTimeout = setTimeout(() => {
      console.warn('⚠️ Mode switch taking too long, re-enabling button as safety measure');
      if (this.modeToggle) {
        this.modeToggle.disabled = false;
        this.updateModeToggle();
      }
    }, 35000); // 35 seconds safety timeout

    try {
      // Update scene manager with timeout protection
      const modeChangePromise = this.sceneManager.setVisualizationMode(targetMode);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Mode switch timeout after 30 seconds')), 30000);
      });

      await Promise.race([modeChangePromise, timeoutPromise]);

      // Update current mode only after successful switch
      this.currentMode = targetMode;

      // Update UI
      this.updateModeToggle();

      // Show mode-specific help
      this.showModeHelp(this.currentMode);

      console.log(`✅ Mode switched successfully to: ${this.currentMode}`);
      this.showNotification(`Switched to ${this.currentMode} mode`, 'success');

    } catch (error) {
      console.error('❌ Failed to switch mode:', error);
      this.showNotification(`Failed to switch to ${targetMode} mode: ${error.message}`, 'error');

      // Reset UI to current mode on error
      this.updateModeToggle();
    } finally {
      // Clear safety timeout
      clearTimeout(safetyTimeout);

      // Always re-enable button
      if (this.modeToggle) {
        this.modeToggle.disabled = false;
        console.log('🔓 Mode toggle button re-enabled');
      }
    }
  }

  /**
   * Handle speed change
   */
  onSpeedChange() {
    if (!this.speedSlider || !this.speedValue) return;

    const speed = parseFloat(this.speedSlider.value);

    // Update navigation controls
    this.navigationControls.setMoveSpeed(speed * 10); // Scale for better feel

    // Update display
    this.speedValue.textContent = `${speed.toFixed(1)}x`;

    console.log(`🚀 Speed changed to: ${speed}x`);
  }

  /**
   * Handle orbit lines toggle
   */
  onOrbitLinesToggle() {
    if (!this.orbitLinesToggle) return;

    const isVisible = this.orbitLinesToggle.checked;

    // Update scene manager
    this.sceneManager.setOrbitLinesVisible(isVisible);

    console.log(`🛸 Orbit lines ${isVisible ? 'enabled' : 'disabled'}`);

    // Show notification
    this.showNotification(
      `Orbit lines ${isVisible ? 'enabled' : 'disabled'}`,
      'info'
    );
  }

  /**
   * Handle zoom to fit button click
   */
  async onZoomToFit() {
    if (!this.zoomToFitButton) return;

    // Check if already animating
    if (this.sceneManager.isCameraAnimating()) {
      console.log('🎬 Camera animation already in progress');
      this.showNotification('Camera animation already in progress', 'warning');
      return;
    }

    // Check if traveling - don't allow zoom during travel
    const travelSystem = this.sceneManager.getPlanetTravelSystem();
    if (travelSystem && travelSystem.isTransitioning) {
      console.log('⚠️ Cannot zoom to fit while traveling');
      this.showNotification('Cannot zoom to fit while traveling', 'warning');
      return;
    }

    // Disable button during animation
    this.zoomToFitButton.disabled = true;
    this.zoomToFitButton.textContent = 'Zooming...';

    try {
      console.log('🔍 Starting zoom to fit animation');

      // Check if orbit lines will be enabled
      const orbitLinesWereVisible = this.sceneManager.areOrbitLinesVisible();

      this.showNotification('Zooming to fit solar system...', 'info');

      // Perform zoom to fit
      await this.sceneManager.zoomToFit(2000, 1.2);

      console.log('✅ Zoom to fit completed successfully');

      // Show appropriate completion message
      const orbitLinesNowVisible = this.sceneManager.areOrbitLinesVisible();
      let completionMessage = 'Zoom to fit completed';

      if (!orbitLinesWereVisible && orbitLinesNowVisible) {
        completionMessage = 'Zoom to fit completed - orbit lines enabled for better view';
      }

      this.showNotification(completionMessage, 'success');

    } catch (error) {
      console.error('❌ Zoom to fit failed:', error);
      this.showNotification('Failed to zoom to fit', 'error');
    } finally {
      // Re-enable button
      this.zoomToFitButton.disabled = false;
      this.zoomToFitButton.innerHTML = '<span class="zoom-icon">🔍</span><span class="zoom-text">Zoom to Fit</span>';
    }
  }

  /**
   * Handle planet selection
   */
  async onPlanetSelect(event) {
    // Prevent event bubbling and multiple handling
    event.preventDefault();
    event.stopPropagation();

    console.log(`🔍 Planet button clicked:`, event.target.dataset.planet);

    const planetName = event.target.dataset.planet;
    if (!planetName) return;

    // Check if already traveling
    const travelSystem = this.sceneManager.getPlanetTravelSystem();
    if (travelSystem && travelSystem.isTransitioning) {
      console.log(`⚠️ Already traveling to ${travelSystem.getCurrentTarget()}, ignoring click on ${planetName}`);
      return;
    }

    this.selectedPlanet = planetName;

    // Update info panel
    this.updateInfoPanel(planetName);

    // Disable all planet buttons during travel
    this.setButtonsEnabled(false);

    console.log(`🪐 Starting travel to: ${planetName}`);

    // Travel to the selected planet
    if (travelSystem) {
      const speed = this.speedSlider ? parseFloat(this.speedSlider.value) : 1.0;

      try {
        await this.sceneManager.travelTo(planetName, speed);
        this.showNotification(`Arrived at ${planetName}`, 'success');
        console.log(`✅ Successfully arrived at ${planetName}`);
      } catch (error) {
        console.error('Travel failed:', error);
        this.showNotification(`Failed to travel to ${planetName}`, 'error');
      } finally {
        // Re-enable buttons after travel
        this.setButtonsEnabled(true);
      }
    }
  }

  /**
   * Handle help toggle
   */
  onHelpToggle() {
    this.isHelpVisible = !this.isHelpVisible;

    if (this.helpPanel) {
      if (this.isHelpVisible) {
        this.helpPanel.classList.remove('collapsed');
      } else {
        this.helpPanel.classList.add('collapsed');
      }
    }

    console.log(`❓ Help panel ${this.isHelpVisible ? 'shown' : 'hidden'}`);
  }

  /**
   * Update mode toggle appearance
   */
  updateModeToggle() {
    if (!this.modeToggle) return;

    const icon = this.modeToggle.querySelector('.mode-icon');
    const text = this.modeToggle.querySelector('.mode-text');

    switch (this.currentMode) {
      case 'realistic':
        this.modeToggle.className = 'mode-button realistic';
        if (icon) icon.textContent = '🔭';
        if (text) text.textContent = 'Realistic';
        break;
      case 'exploration':
        this.modeToggle.className = 'mode-button exploration';
        if (icon) icon.textContent = '🚀';
        if (text) text.textContent = 'Exploration';
        break;
      case 'artistic':
        this.modeToggle.className = 'mode-button artistic';
        if (icon) icon.textContent = '🎨';
        if (text) text.textContent = 'Artistic';
        break;
    }
  }

  /**
   * Force reset the mode toggle button state (useful for debugging)
   */
  resetModeToggleState() {
    if (this.modeToggle) {
      this.modeToggle.disabled = false;
      this.updateModeToggle();
      console.log('🔧 Mode toggle button state reset');
    }
  }

  /**
   * Show mode-specific help information
   */
  showModeHelp(mode) {
    const helpMessages = {
      realistic: '🔭 Realistic Mode: True astronomical distances. Distant planets may be invisible. Press T for telescope mode.',
      exploration: '🚀 Exploration Mode: Balanced distances for navigation. Good for learning and travel.',
      artistic: '🎨 Artistic Mode: Enhanced visuals and compressed distances for beautiful views.'
    };

    const message = helpMessages[mode];
    if (message) {
      this.showNotification(message, 'info', 4000);
    }
  }

  /**
   * Handle keyboard shortcuts
   */
  onKeyDown(event) {
    // Ignore if typing in input fields
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }

    switch (event.key.toLowerCase()) {
      case 't':
        // Toggle telescope mode
        event.preventDefault();
        this.toggleTelescopeMode();
        break;
      case 'm':
        // Toggle visualization mode
        event.preventDefault();
        this.onModeToggle();
        break;
      case 'o':
        // Toggle orbit lines
        event.preventDefault();
        this.toggleOrbitLines();
        break;
      case 'f':
        // Zoom to fit
        event.preventDefault();
        this.onZoomToFit();
        break;
      case '+':
      case '=':
        // Zoom in telescope
        event.preventDefault();
        this.telescopeZoomIn();
        break;
      case '-':
        // Zoom out telescope
        event.preventDefault();
        this.telescopeZoomOut();
        break;
    }
  }

  /**
   * Toggle telescope mode
   */
  toggleTelescopeMode() {
    const telescopeSystem = this.sceneManager.getTelescopeSystem();
    if (!telescopeSystem) {
      this.showNotification('Telescope only available in realistic mode', 'warning');
      return;
    }

    const isActive = this.sceneManager.toggleTelescopeMode();
    const message = isActive ?
      '🔭 Telescope activated. Use +/- to zoom, ESC to exit.' :
      '🔭 Telescope deactivated.';

    this.showNotification(message, 'info');
  }

  /**
   * Zoom in telescope
   */
  telescopeZoomIn() {
    const telescopeSystem = this.sceneManager.getTelescopeSystem();
    if (telescopeSystem && telescopeSystem.isTelescopeMode) {
      telescopeSystem.zoomIn();
    }
  }

  /**
   * Zoom out telescope
   */
  telescopeZoomOut() {
    const telescopeSystem = this.sceneManager.getTelescopeSystem();
    if (telescopeSystem && telescopeSystem.isTelescopeMode) {
      telescopeSystem.zoomOut();
    }
  }

  /**
   * Toggle orbit lines visibility
   */
  toggleOrbitLines() {
    if (!this.orbitLinesToggle) return;

    // Toggle the checkbox state
    this.orbitLinesToggle.checked = !this.orbitLinesToggle.checked;

    // Trigger the change event
    this.onOrbitLinesToggle();
  }

  /**
   * Update info panel with planet information
   */
  updateInfoPanel(planetName) {
    if (!this.currentTarget || !this.targetInfo) return;

    // Planet data (simplified for now)
    const planetData = {
      sun: {
        name: 'The Sun',
        info: 'Our star - the center of the solar system. A massive ball of hot plasma held together by gravity.',
      },
      mercury: {
        name: 'Mercury',
        info: 'The smallest planet and closest to the Sun. Extreme temperature variations.',
      },
      venus: {
        name: 'Venus',
        info: 'The hottest planet with a thick, toxic atmosphere. Often called Earth\'s twin.',
      },
      earth: {
        name: 'Earth',
        info: 'Our home planet. The only known planet with life in the universe.',
      },
      mars: {
        name: 'Mars',
        info: 'The Red Planet. Has the largest volcano and canyon in the solar system.',
      },
      jupiter: {
        name: 'Jupiter',
        info: 'The largest planet. A gas giant with a Great Red Spot storm.',
      },
      saturn: {
        name: 'Saturn',
        info: 'Famous for its beautiful ring system. A gas giant less dense than water.',
      },
      uranus: {
        name: 'Uranus',
        info: 'An ice giant that rotates on its side. Has a faint ring system.',
      },
      neptune: {
        name: 'Neptune',
        info: 'The windiest planet with speeds up to 2,100 km/h. Deep blue color.',
      },
    };

    const data = planetData[planetName];
    if (data) {
      this.currentTarget.textContent = data.name;
      this.targetInfo.innerHTML = `<p>${data.info}</p>`;
    }
  }

  /**
   * Update UI elements
   */
  updateUI() {
    this.updateModeToggle();

    // Set initial speed value
    if (this.speedSlider && this.speedValue) {
      const speed = parseFloat(this.speedSlider.value);
      this.speedValue.textContent = `${speed.toFixed(1)}x`;
    }
  }

  /**
   * Update method called from main loop
   */
  update(_deltaTime) {
    // Update travel progress if traveling
    const travelSystem = this.sceneManager.getPlanetTravelSystem();
    if (travelSystem && travelSystem.isTransitioning) {
      const progress = travelSystem.getTransitionProgress();
      const target = travelSystem.getCurrentTarget();

      if (target && this.currentTarget) {
        this.currentTarget.textContent = `Traveling to ${target}... ${Math.round(progress * 100)}%`;
      }
    }
  }

  /**
   * Show notification message
   */
  showNotification(message, type = 'info') {
    // TODO: Implement notification system
    console.log(`📢 ${type.toUpperCase()}: ${message}`);
  }

  /**
   * Get current visualization mode
   */
  getCurrentMode() {
    return this.currentMode;
  }

  /**
   * Get selected planet
   */
  getSelectedPlanet() {
    return this.selectedPlanet;
  }

  /**
   * Enable/disable planet buttons
   */
  setButtonsEnabled(enabled) {
    this.planetButtons.forEach(button => {
      button.disabled = !enabled;
      if (enabled) {
        button.style.opacity = '1';
        button.style.cursor = 'pointer';
      } else {
        button.style.opacity = '0.5';
        button.style.cursor = 'not-allowed';
      }
    });
  }

  /**
   * Dispose of the UI manager
   */
  dispose() {
    // Remove event listeners
    if (this.modeToggle) {
      this.modeToggle.removeEventListener('click', this.onModeToggle);
    }

    if (this.speedSlider) {
      this.speedSlider.removeEventListener('input', this.onSpeedChange);
    }

    this.planetButtons.forEach(button => {
      button.removeEventListener('click', this.onPlanetSelect);
    });

    if (this.helpToggle) {
      this.helpToggle.removeEventListener('click', this.onHelpToggle);
    }

    console.log('🧹 UIManager disposed');
  }
}
